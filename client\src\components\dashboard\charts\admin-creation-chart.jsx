import React, { useState, useEffect } from "react";
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { getAdminChartData } from "@/api/dashboard-api";
import { toast } from "sonner";

const chartConfig = {
  schools: {
    label: "Schools",
    color: "hsl(var(--primary))",
  },
  schoolAdmins: {
    label: "School Admins",
    color: "hsl(var(--chart-2))",
  },
};

export function AdminCreationChart() {
  const isMobile = useIsMobile();
  const [timeRange, setTimeRange] = useState("30d");
  const [chartData, setChartData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isMobile) {
      setTimeRange("7d");
    }
  }, [isMobile]);

  useEffect(() => {
    fetchChartData();
  }, [timeRange]);

  const fetchChartData = async () => {
    setIsLoading(true);
    try {
      const response = await getAdminChartData(timeRange);
      if (response.success) {
        setChartData(response.data);
      } else {
        throw new Error(response.message || "Failed to fetch chart data");
      }
    } catch (error) {
      console.error("Error fetching chart data:", error);
      toast.error("Failed to load chart data", {
        description: error.message || "Please try refreshing the page",
      });
      setChartData([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getTimeRangeLabel = () => {
    switch (timeRange) {
      case "7d":
        return "Last 7 days";
      case "30d":
        return "Last 30 days";
      case "90d":
        return "Last 3 months";
      default:
        return "Last 30 days";
    }
  };

  const getTotalCounts = () => {
    const totalSchools = chartData.reduce((sum, item) => sum + item.schools, 0);
    const totalSchoolAdmins = chartData.reduce(
      (sum, item) => sum + item.schoolAdmins,
      0
    );
    return { totalSchools, totalSchoolAdmins };
  };

  const { totalSchools, totalSchoolAdmins } = getTotalCounts();

  if (isLoading) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>Schools & Admins Creation</CardTitle>
          <CardDescription>Loading chart data...</CardDescription>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          <div className="aspect-auto h-[250px] w-full animate-pulse bg-muted rounded-md" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Schools & Admins Creation</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">
            {totalSchools} schools and {totalSchoolAdmins} admins created in{" "}
            {getTimeRangeLabel().toLowerCase()}
          </span>
          <span className="@[540px]/card:hidden">
            {totalSchools} schools, {totalSchoolAdmins} admins
          </span>
        </CardDescription>
        <CardAction>
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={setTimeRange}
            variant="outline"
            className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
          >
            <ToggleGroupItem value="90d">Last 3 months</ToggleGroupItem>
            <ToggleGroupItem value="30d">Last 30 days</ToggleGroupItem>
            <ToggleGroupItem value="7d">Last 7 days</ToggleGroupItem>
          </ToggleGroup>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger
              className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
              size="sm"
              aria-label="Select a value"
            >
              <SelectValue placeholder="Last 30 days" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="90d" className="rounded-lg">
                Last 3 months
              </SelectItem>
              <SelectItem value="30d" className="rounded-lg">
                Last 30 days
              </SelectItem>
              <SelectItem value="7d" className="rounded-lg">
                Last 7 days
              </SelectItem>
            </SelectContent>
          </Select>
        </CardAction>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="fillSchools" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-schools)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-schools)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillSchoolAdmins" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-schoolAdmins)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-schoolAdmins)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isMobile ? -1 : 10}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="schoolAdmins"
              type="natural"
              fill="url(#fillSchoolAdmins)"
              stroke="var(--color-schoolAdmins)"
              stackId="a"
            />
            <Area
              dataKey="schools"
              type="natural"
              fill="url(#fillSchools)"
              stroke="var(--color-schools)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
