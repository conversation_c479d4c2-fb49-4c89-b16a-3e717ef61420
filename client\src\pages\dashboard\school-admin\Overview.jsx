import React, { useEffect } from "react";
import { Users, BookOpen, Calendar, GraduationCap } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";
import { useDashboard } from "@/context/dashboard-context";

const SchoolAdminOverview = () => {
  const { user, isLoading: authLoading } = useAuth();
  const {
    dashboardData,
    isLoading: dashboardLoading,
    fetchSchoolAdminDashboardStats,
  } = useDashboard();

  useEffect(() => {
    if (user && user.role === "school-admin") {
      fetchSchoolAdminDashboardStats();
    }
  }, [user]);

  const isLoading = authLoading || dashboardLoading;
  const stats = dashboardData?.overview;

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={authLoading}
        title="School Admin Dashboard"
        subtitle="Manage your school's students, teachers, and academic programs"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Students"
          value={stats?.totalStudents?.current || 0}
          description={`+${
            stats?.totalStudents?.lastMonth || 0
          } from last month`}
          icon={Users}
          isLoading={isLoading}
          trend={stats?.totalStudents?.trend || "neutral"}
        />

        <StatCard
          title="Total Teachers"
          value={stats?.totalTeachers?.current || 0}
          description={`+${
            stats?.totalTeachers?.lastMonth || 0
          } from last month`}
          icon={GraduationCap}
          isLoading={isLoading}
          trend={stats?.totalTeachers?.trend || "neutral"}
        />

        <StatCard
          title="Total Notifications"
          value={stats?.totalNotifications?.current || 0}
          description={`+${
            stats?.totalNotifications?.lastMonth || 0
          } from last month`}
          icon={BookOpen}
          isLoading={isLoading}
          trend={stats?.totalNotifications?.trend || "neutral"}
        />

        <StatCard
          title="School Activity"
          value={stats?.totalNotifications?.current || 0}
          description="Total notifications sent"
          icon={Calendar}
          isLoading={isLoading}
          trend="neutral"
        />
      </div>

      <ChartAreaInteractive />
    </div>
  );
};

export default SchoolAdminOverview;
