import React, { useEffect } from "react";
import { Activity, Users, School, UserCheck } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";
import { useDashboard } from "@/context/dashboard-context";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";
import { SchoolAdminColumns } from "./school-admin/school-admin-columns";
import { SchoolColumns } from "./schools/school-columns";
import { ContactColumns } from "./contact/contact-columns";
import { useSchoolAdmin } from "@/context/school-admin-context";
import { useContact } from "@/context/contact-context";
import { useSchool } from "@/context/school-context";
import { AdminCreationChart } from "@/components/dashboard/charts/admin-creation-chart";

const AdminOverview = () => {
  const { user, isLoading: authLoading } = useAuth();
  const {
    dashboardData,
    isLoading: dashboardLoading,
    fetchAdminDashboardStats,
  } = useDashboard();
  const {
    schoolAdmins,
    isLoading: schoolAdminsLoading,
    fetchAllSchoolAdmins,
  } = useSchoolAdmin();
  const {
    contacts,
    isLoading: contactsLoading,
    fetchAllContacts,
  } = useContact();
  const { schools, isLoading: schoolsLoading, fetchAllSchools } = useSchool();

  const isLoading = authLoading || dashboardLoading;
  const stats = dashboardData?.overview;

  useEffect(() => {
    if (user && user.role === "admin") {
      fetchAdminDashboardStats();
    }
  }, [user]);

  useEffect(() => {
    fetchAllSchools();
  }, []);

  useEffect(() => {
    fetchAllContacts();
  }, []);

  useEffect(() => {
    fetchAllSchoolAdmins();
  }, []);

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={authLoading}
        title="Admin Dashboard"
        subtitle="Manage your schools, users, and system settings"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Schools"
          value={stats?.totalSchools?.current || 0}
          description={`+${
            stats?.totalSchools?.lastMonth || 0
          } from last month`}
          icon={School}
          isLoading={isLoading}
          trend={stats?.totalSchools?.trend || "neutral"}
        />

        <StatCard
          title="Total Users"
          value={stats?.totalUsers?.current || 0}
          description={`+${stats?.totalUsers?.lastMonth || 0} from last month`}
          icon={Users}
          isLoading={isLoading}
          trend={stats?.totalUsers?.trend || "neutral"}
        />

        <StatCard
          title="Active Teachers"
          value={stats?.activeTeachers?.current || 0}
          description={`+${
            stats?.activeTeachers?.lastMonth || 0
          } from last month`}
          icon={UserCheck}
          isLoading={isLoading}
          trend={stats?.activeTeachers?.trend || "neutral"}
        />

        <StatCard
          title="System Activity"
          value={stats?.systemActivity?.current || 0}
          description={`${
            stats?.systemActivity?.current > stats?.systemActivity?.lastMonth
              ? "+"
              : ""
          }${
            (stats?.systemActivity?.current || 0) -
            (stats?.systemActivity?.lastMonth || 0)
          } from last month`}
          icon={Activity}
          isLoading={isLoading}
          trend={stats?.systemActivity?.trend || "neutral"}
        />
      </div>

      <AdminCreationChart />

      <Tabs defaultValue="schools" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="schools">Schools</TabsTrigger>
          <TabsTrigger value="school-admins">School Admins</TabsTrigger>
          <TabsTrigger value="contacts">Contacts</TabsTrigger>
        </TabsList>
        <TabsContent value="schools">
          {schoolsLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={schools}
              columns={SchoolColumns()}
              model="school"
            />
          )}
        </TabsContent>
        <TabsContent value="school-admins">
          {schoolAdminsLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={schoolAdmins}
              columns={SchoolAdminColumns()}
              model="school-admin"
            />
          )}
        </TabsContent>
        <TabsContent value="contacts">
          {contactsLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={contacts}
              columns={ContactColumns}
              model="contact"
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminOverview;
