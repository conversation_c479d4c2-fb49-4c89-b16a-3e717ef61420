/**
 * Chart Examples - Demonstrates different use cases of the ChartAreaInteractive component
 */

import React from "react";
import { ChartAreaInteractive } from "../chart-area-interactive";

// Sample data for different chart types
const sampleData = [
  { date: "2024-06-24", students: 132, teachers: 18, notifications: 45 },
  { date: "2024-06-25", students: 141, teachers: 19, notifications: 52 },
  { date: "2024-06-26", students: 134, teachers: 18, notifications: 38 },
  { date: "2024-06-27", students: 148, teachers: 20, notifications: 61 },
  { date: "2024-06-28", students: 149, teachers: 19, notifications: 43 },
  { date: "2024-06-29", students: 143, teachers: 18, notifications: 39 },
  { date: "2024-06-30", students: 156, teachers: 21, notifications: 58 },
];

// Example 1: Simple two-area chart
export function StudentsTeachersChart() {
  return (
    <ChartAreaInteractive
      title="Students & Teachers"
      description="Active students and teachers over time"
      data={sampleData}
      config={{
        students: {
          label: "Students",
          color: "hsl(var(--primary))",
        },
        teachers: {
          label: "Teachers",
          color: "hsl(var(--chart-2))",
        },
      }}
      dataKeys={["students", "teachers"]}
    />
  );
}

// Example 2: Three-area chart with custom time ranges
export function ActivityChart() {
  const customTimeRanges = [
    { value: "7d", label: "Last week" },
    { value: "30d", label: "Last month" },
    { value: "90d", label: "Last quarter" },
  ];

  return (
    <ChartAreaInteractive
      title="School Activity Overview"
      description="Students, teachers, and notifications activity"
      data={sampleData}
      config={{
        students: {
          label: "Students",
          color: "hsl(var(--chart-1))",
        },
        teachers: {
          label: "Teachers",
          color: "hsl(var(--chart-2))",
        },
        notifications: {
          label: "Notifications",
          color: "hsl(var(--chart-3))",
        },
      }}
      dataKeys={["students", "teachers", "notifications"]}
      timeRangeOptions={customTimeRanges}
      defaultTimeRange="7d"
      height="300px"
    />
  );
}

// Example 3: Chart without time range selector
export function SimpleChart() {
  return (
    <ChartAreaInteractive
      title="Simple Data View"
      description="Basic chart without time range controls"
      data={sampleData}
      config={{
        students: {
          label: "Students",
          color: "hsl(var(--primary))",
        },
      }}
      dataKeys={["students"]}
      showTimeRangeSelector={false}
      height="200px"
    />
  );
}

// Example 4: Loading state
export function LoadingChart() {
  return (
    <ChartAreaInteractive
      title="Loading Example"
      description="Chart in loading state"
      isLoading={true}
      height="250px"
    />
  );
}

// Example 5: Chart with callback for time range changes
export function InteractiveChart() {
  const handleTimeRangeChange = (timeRange) => {
    console.log("Time range changed to:", timeRange);
    // Here you would typically fetch new data based on the time range
  };

  return (
    <ChartAreaInteractive
      title="Interactive Chart"
      description="Chart with time range callback"
      data={sampleData}
      config={{
        students: {
          label: "Students",
          color: "hsl(var(--primary))",
        },
        teachers: {
          label: "Teachers",
          color: "hsl(var(--chart-2))",
        },
      }}
      dataKeys={["students", "teachers"]}
      onTimeRangeChange={handleTimeRangeChange}
    />
  );
}
